import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';

/// شاشة تسجيل الدخول الجديدة
class NewLoginScreen extends HookConsumerWidget {
  const NewLoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isLoading = useState(false);

    // Handle authentication state - only redirect after successful completion
    ref.listen(unifiedAuthProviderProvider, (previous, next) {
      // Check if widget is still mounted before updating state
      if (!context.mounted) return;

      if (isLoading.value) {
        next.when(
          initial: () {},
          loading: (message, operation) {},
          authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) {
            if (context.mounted) {
              isLoading.value = false;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('مرحباً بك ${user.firstName}! جاري التوجه للصفحة الرئيسية...'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ),
              );
              Future.delayed(const Duration(milliseconds: 1500), () {
                if (context.mounted) context.go('/');
              });
            }
          },
          unauthenticated: (reason) {
            if (context.mounted) isLoading.value = false;
          },
          error: (message, errorCode, errorType, isRecoverable, originalException) {
            if (context.mounted) isLoading.value = false;
          },
          emailVerificationPending: (email, sentAt) {
            if (context.mounted) isLoading.value = false;
          },
          sessionExpired: (expiredAt, autoRefreshAttempted) {
            if (context.mounted) isLoading.value = false;
          },
        );
      }
    });

    Future<void> handleEmailSignIn() async {
      if (!formKey.currentState!.validate()) return;
      if (!context.mounted) return;

      isLoading.value = true;
      try {
        final result = await ref
            .read(unifiedAuthProviderProvider.notifier)
            .signInWithEmail(
              email: emailController.text.trim(),
              password: passwordController.text,
            );

        if (!context.mounted) return;

        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            // Success handled by listener
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            if (context.mounted) {
              isLoading.value = false;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('خطأ في تسجيل الدخول: $error'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 4),
                ),
              );
            }
          },
          cancelled: (reason) {
            if (context.mounted) isLoading.value = false;
          },
          pending: (message, pendingAction, actionData) {},
        );
      } finally {
        if (context.mounted) isLoading.value = false;
      }
    }

    Future<void> handleGoogleSignIn() async {
      if (isLoading.value) return; // Prevent multiple submissions
      if (!context.mounted) return;

      isLoading.value = true;
      try {
        final result = await ref
            .read(unifiedAuthProviderProvider.notifier)
            .signInWithGoogle();

        if (!context.mounted) return;

        result.when(
          success: (user, token, refreshToken, tokenExpiry, metadata) {
            // Success handled by listener - don't set loading to false here
          },
          failure: (error, errorCode, errorType, isRecoverable, details) {
            if (context.mounted) {
              isLoading.value = false;

              // Provide better error messages for common issues
              String userMessage = 'خطأ في تسجيل الدخول بـ Google';
              if (error.contains('Connection refused') ||
                  error.contains('connection error') ||
                  error.contains('connection timeout')) {
                userMessage = 'مشكلة في الاتصال بالسيرفر. يرجى المحاولة مرة أخرى.';
              } else if (error.contains('Google')) {
                userMessage = 'خطأ في تسجيل الدخول بـ Google. يرجى المحاولة مرة أخرى.';
              }

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(userMessage),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 4),
                  action: SnackBarAction(
                    label: 'إعادة المحاولة',
                    textColor: Colors.white,
                    onPressed: () => handleGoogleSignIn(),
                  ),
                ),
              );
            }
          },
          cancelled: (reason) {
            if (context.mounted) {
              isLoading.value = false;
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إلغاء تسجيل الدخول'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          pending: (message, pendingAction, actionData) {
            // Keep loading state during pending
          },
        );
      } catch (e) {
        if (context.mounted) {
          isLoading.value = false;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ غير متوقع: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    return Scaffold(
      appBar: AppBar(title: const Text('تسجيل الدخول'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo or App Name
              const Icon(Icons.car_rental, size: 80, color: Colors.blue),
              const SizedBox(height: 32),
              const Text(
                'مرحباً بك في CarNow',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 32),

              // Email Field
              TextFormField(
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال البريد الإلكتروني';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'يرجى إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Password Field
              TextFormField(
                controller: passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  prefixIcon: Icon(Icons.lock),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Email Sign In Button
              ElevatedButton(
                onPressed: isLoading.value ? null : handleEmailSignIn,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: isLoading.value
                    ? const CircularProgressIndicator()
                    : const Text(
                        'تسجيل الدخول',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
              const SizedBox(height: 16),

              // Divider
              const Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text('أو'),
                  ),
                  Expanded(child: Divider()),
                ],
              ),
              const SizedBox(height: 16),

              // Enhanced Google Sign In Button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: isLoading.value ? null : handleGoogleSignIn,
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              'G',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'تسجيل الدخول بـ Google',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Sign Up Link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('ليس لديك حساب؟'),
                  TextButton(
                    onPressed: () => context.go('/signup'),
                    child: const Text('إنشاء حساب جديد'),
                  ),
                ],
              ),

              // Forgot Password Link
              TextButton(
                onPressed: () => context.go('/forgot-password'),
                child: const Text('نسيت كلمة المرور؟'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
